{% load crispy_forms_field %}

{% if field.is_hidden %}
    {{ field }}
{% else %}
    <div id="div_{{ field.auto_id }}" class="form-group{% if wrapper_class %} {{ wrapper_class }}{% endif %}{% if 'form-horizontal' in form_class %} row{% endif %}{% if form_group_wrapper_class %} {{ form_group_wrapper_class }}{% endif %}{% if field.css_classes %} {{ field.css_classes }}{% endif %}">

        {% if field.label and form_show_labels %}
            <label for="{{ field.id_for_label }}" class="{% if 'form-horizontal' in form_class %}col-form-label {% endif %}{{ label_class }}{% if field.field.required %} requiredField{% endif %}">
                {{ field.label }}{% if field.field.required %}<span class="asteriskField">*</span>{% endif %}
            </label>
        {% endif %}

        <div class="{{ field_class }}">
            <div class="input-group{% if input_size %} {{ input_size }}{% endif %}">
                {% if crispy_prepended_text %}
                  <div class="input-group-prepend{% if active %} active{% endif %}">
                    <span class="input-group-text">{{ crispy_prepended_text }}</span>
                  </div>
                {% endif %}
                {% if field|is_select and use_custom_control %}
                    {% if field.errors %}
                        {% crispy_field field 'class' 'custom-select is-invalid' %}
                    {% else %}
                        {% crispy_field field 'class' 'custom-select' %}
                    {% endif %}
                {% else %}
                    {% if field.errors %}
                        {% crispy_field field 'class' 'form-control is-invalid' %}
                    {% else %}
                        {% crispy_field field 'class' 'form-control' %}
                    {% endif %}
                {% endif %}
                {% if crispy_appended_text %}
                  <div class="input-group-append{% if active %} active{% endif %}">
                    <span class="input-group-text">{{ crispy_appended_text }}</span>
                  </div>
                {% endif %}
                {% if error_text_inline %}
                    {% include 'bootstrap4/layout/field_errors.html' %}
                {% else %}
                    {% include 'bootstrap4/layout/field_errors_block.html' %}
                {% endif %}
            </div>
        {% if not help_text_inline %}
            {% include 'bootstrap4/layout/help_text.html' %}
        {% endif %}
        </div>

    </div>
{% endif %}
