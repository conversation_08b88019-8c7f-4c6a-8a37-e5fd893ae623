{% load crispy_forms_field %}

<div class="{{ field_class }} mb-2">
{% for widget in field.subwidgets %}
{% if widget.data.is_initial %}
<div class="input-group mb-2">
    <div class="input-group-prepend">
        <span class="input-group-text">{{ widget.data.initial_text }}</span>
    </div>
    <div class="form-control d-flex h-auto">
        <span class="text-break" style="flex-grow:1;min-width:0">
        <a href="{{ field.value.url }}">{{ field.value }}</a>
        </span>
        {% if not widget.data.required %}
        <span class="align-self-center ml-2">
            <span class="custom-control custom-checkbox">
                <input type="checkbox" name="{{ widget.data.checkbox_name }}" id="{{ widget.data.checkbox_id }}" class="custom-control-input"{% if field.field.disabled %} disabled{% endif %} >
                <label class="custom-control-label mb-0" for="{{ widget.data.checkbox_id }}">{{ widget.data.clear_checkbox_label }}</label>
            </span>
        </span>
    {% endif %}
    </div>
</div>
<div class="input-group mb-0">
    <div class="input-group-prepend">
        <span class="input-group-text">{{ widget.data.input_text }}</span>
    </div>
{% endif %}
    <div class="form-control custom-file{% if field.errors %} is-invalid{%endif%}" style="border:0">
        <input type="{{ widget.data.type }}" name="{{ widget.data.name }}" class="custom-file-input{% if widget.data.attrs.class %} {{ widget.data.attrs.class }}{% endif %}{% if field.errors %} is-invalid{%endif%}"{% if field.field.disabled %} disabled{% endif %}{% for name, value in widget.data.attrs.items %}{% if value is not False and name != 'class' %} {{ name }}{% if value is not True %}="{{ value|stringformat:'s' }}"{% endif %}{% endif %}{% endfor %}>
        <label class="custom-file-label text-truncate" for="{{ field.id_for_label }}">---</label>
        <script type="text/javascript" id="script-{{ field.id_for_label }}">        
            document.getElementById("script-{{ field.id_for_label }}").parentNode.querySelector('.custom-file-input').onchange =  function (e){
                var filenames = "";
                for (let i=0;i<e.target.files.length;i++){
                    filenames+=(i>0?", ":"")+e.target.files[i].name;
                }
                e.target.parentNode.querySelector('.custom-file-label').textContent=filenames;    
            }        
        </script>
    </div>
    {% if not widget.data.is_initial %}
    {% include 'bootstrap4/layout/help_text_and_errors.html' %}
    {% endif %}
{% if widget.data.is_initial %}
</div>
<div class="input-group mb-0">
{% include 'bootstrap4/layout/help_text_and_errors.html' %}
</div>
{% endif %}
{% endfor %}
</div>
