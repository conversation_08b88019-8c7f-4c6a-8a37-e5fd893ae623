{% load crispy_forms_field %}
<div id="div_{{ field.auto_id }}" class="form-group{% if 'form-horizontal' in form_class %} row{% endif %}{% if form_show_errors and field.errors %} error{% endif %}{% if field.css_classes %} {{ field.css_classes }}{% endif %}">
    <label class="{% if 'form-horizontal' in form_class %}col-form-label {% endif %}{{ label_class }}{% if field.field.required %} requiredField{% endif %}">{{ field.label }}{% if field.field.required %}<span class="asteriskField">*</span>{% endif %}</label>
    <div class="{{ field_class }}">
        {% if field|is_select and use_custom_control %}
            {% crispy_field field 'class' 'custom-select' 'disabled' 'disabled' %}
        {% elif field|is_file %}
            {% crispy_field field 'class' 'form-control-file' 'disabled' 'disabled' %}
        {% else %}
            {% crispy_field field 'class' 'form-control' 'disabled' 'disabled' %}
        {% endif %}
        {% include 'bootstrap4/layout/help_text.html' %}
    </div>
</div>
